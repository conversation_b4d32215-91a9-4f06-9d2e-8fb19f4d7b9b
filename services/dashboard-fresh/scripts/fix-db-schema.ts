#!/usr/bin/env -S deno run -A
// Database Schema Fix Script
// Fixes existing database schema to match application expectations

import { Client } from "postgres";

const DB_CONFIG = {
  hostname: Deno.env.get("DB_HOST") || "localhost",
  port: parseInt(Deno.env.get("DB_PORT") || "5432"),
  user: Deno.env.get("DB_USER") || "postgres",
  password: Deno.env.get("DB_PASSWORD") || "password",
  database: Deno.env.get("DB_NAME") || "ecommerce_analytics",
  tls: {
    enabled: false,
    enforce: false,
  },
};

async function fixDatabaseSchema() {
  console.log("🔧 Fixing database schema...");
  
  let client: Client | null = null;
  
  try {
    client = new Client(DB_CONFIG);
    await client.connect();
    console.log("✅ Connected to database");
    
    // Check current table structure
    console.log("🔍 Checking current table structures...");
    
    // Check link_clicks table structure
    const linkClicksColumns = await client.queryArray(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'link_clicks' 
      ORDER BY ordinal_position
    `);
    
    console.log("📋 Current link_clicks columns:");
    for (const row of linkClicksColumns.rows) {
      console.log(`   - ${row[0]} (${row[1]}, nullable: ${row[2]})`);
    }
    
    // Check if tenant_id column exists in link_clicks
    const hasTenantId = linkClicksColumns.rows.some(row => row[0] === 'tenant_id');
    const hasClickedAt = linkClicksColumns.rows.some(row => row[0] === 'clicked_at');
    
    if (!hasTenantId) {
      console.log("➕ Adding tenant_id column to link_clicks...");
      await client.queryArray(`
        ALTER TABLE link_clicks 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'tenant_demo_456'
      `);
      console.log("✅ Added tenant_id column");
    }
    
    if (!hasClickedAt) {
      console.log("➕ Adding clicked_at column to link_clicks...");
      await client.queryArray(`
        ALTER TABLE link_clicks 
        ADD COLUMN IF NOT EXISTS clicked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      `);
      console.log("✅ Added clicked_at column");
    }
    
    // Check customer_events table
    const customerEventsColumns = await client.queryArray(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'customer_events' 
      ORDER BY ordinal_position
    `);
    
    console.log("📋 Current customer_events columns:");
    for (const row of customerEventsColumns.rows) {
      console.log(`   - ${row[0]} (${row[1]}, nullable: ${row[2]})`);
    }
    
    const hasCustomerEventsTenantId = customerEventsColumns.rows.some(row => row[0] === 'tenant_id');
    
    if (!hasCustomerEventsTenantId) {
      console.log("➕ Adding tenant_id column to customer_events...");
      await client.queryArray(`
        ALTER TABLE customer_events 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'tenant_demo_456'
      `);
      console.log("✅ Added tenant_id column to customer_events");
    }
    
    // Check links table
    const linksColumns = await client.queryArray(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'links' 
      ORDER BY ordinal_position
    `);
    
    console.log("📋 Current links columns:");
    for (const row of linksColumns.rows) {
      console.log(`   - ${row[0]} (${row[1]}, nullable: ${row[2]})`);
    }
    
    const hasLinksTenantId = linksColumns.rows.some(row => row[0] === 'tenant_id');
    
    if (!hasLinksTenantId) {
      console.log("➕ Adding tenant_id column to links...");
      await client.queryArray(`
        ALTER TABLE links 
        ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(255) NOT NULL DEFAULT 'tenant_demo_456'
      `);
      console.log("✅ Added tenant_id column to links");
    }
    
    // Update existing data with tenant_id if needed
    console.log("🔄 Updating existing data with tenant_id...");

    // Generate a demo tenant UUID
    const demoTenantUuid = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'; // Fixed demo UUID

    // Check if we need to update tenant_id values
    const nullTenantClicks = await client.queryArray(`
      SELECT COUNT(*) FROM link_clicks WHERE tenant_id IS NULL
    `);

    if (Number(nullTenantClicks.rows[0][0]) > 0) {
      await client.queryArray(`
        UPDATE link_clicks
        SET tenant_id = $1
        WHERE tenant_id IS NULL
      `, [demoTenantUuid]);
      console.log(`✅ Updated ${nullTenantClicks.rows[0][0]} link_clicks records`);
    }

    const nullTenantEvents = await client.queryArray(`
      SELECT COUNT(*) FROM customer_events WHERE tenant_id IS NULL
    `);

    if (Number(nullTenantEvents.rows[0][0]) > 0) {
      await client.queryArray(`
        UPDATE customer_events
        SET tenant_id = $1
        WHERE tenant_id IS NULL
      `, [demoTenantUuid]);
      console.log(`✅ Updated ${nullTenantEvents.rows[0][0]} customer_events records`);
    }

    const nullTenantLinks = await client.queryArray(`
      SELECT COUNT(*) FROM links WHERE tenant_id IS NULL
    `);

    if (Number(nullTenantLinks.rows[0][0]) > 0) {
      await client.queryArray(`
        UPDATE links
        SET tenant_id = $1
        WHERE tenant_id IS NULL
      `, [demoTenantUuid]);
      console.log(`✅ Updated ${nullTenantLinks.rows[0][0]} links records`);
    }

    console.log("✅ Updated existing data");
    
    // Create missing indexes
    console.log("📊 Creating missing indexes...");
    
    try {
      await client.queryArray(`
        CREATE INDEX IF NOT EXISTS idx_customer_events_tenant_timestamp 
        ON customer_events (tenant_id, timestamp DESC)
      `);
      
      await client.queryArray(`
        CREATE INDEX IF NOT EXISTS idx_customer_events_event_type 
        ON customer_events (event_type)
      `);
      
      await client.queryArray(`
        CREATE INDEX IF NOT EXISTS idx_link_clicks_tenant_clicked_at 
        ON link_clicks (tenant_id, clicked_at DESC)
      `);
      
      await client.queryArray(`
        CREATE INDEX IF NOT EXISTS idx_links_tenant_active 
        ON links (tenant_id, is_active)
      `);
      
      console.log("✅ Indexes created");
    } catch (error) {
      console.warn("⚠️ Some indexes may already exist:", (error as Error).message);
    }
    
    // Insert sample data if tables are empty
    console.log("📦 Checking for sample data...");

    const demoTenantUuid = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

    const eventCount = await client.queryArray(`
      SELECT COUNT(*) FROM customer_events WHERE tenant_id = $1
    `, [demoTenantUuid]);

    if (Number(eventCount.rows[0][0]) === 0) {
      console.log("📦 Inserting sample data...");

      // Insert sample events for the last 7 days
      const now = new Date();
      for (let i = 0; i < 7; i++) {
        const eventDate = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
        const dailyEventCount = Math.floor(Math.random() * 100) + 50;

        for (let j = 0; j < dailyEventCount; j++) {
          const eventTime = new Date(eventDate.getTime() + (Math.random() * 24 * 60 * 60 * 1000));
          const eventId = crypto.randomUUID();

          await client.queryArray(`
            INSERT INTO customer_events (
              id, tenant_id, event_type, event_source, event_data, timestamp
            ) VALUES ($1, $2, $3, $4, $5, $6)
          `, [
            eventId,
            demoTenantUuid,
            ['page_view', 'click', 'conversion', 'signup'][Math.floor(Math.random() * 4)],
            'web',
            JSON.stringify({ page: '/demo', source: 'organic' }),
            eventTime.toISOString()
          ]);
        }
      }

      console.log("✅ Sample data inserted");
    } else {
      console.log(`✅ Found ${eventCount.rows[0][0]} existing events`);
    }
    
    // Test the schema
    console.log("🧪 Testing schema...");
    
    const testQuery = await client.queryArray(`
      SELECT 
        COUNT(*) as total_events,
        COUNT(DISTINCT tenant_id) as tenants,
        MAX(timestamp) as latest_event
      FROM customer_events 
      WHERE tenant_id = 'tenant_demo_456'
    `);
    
    console.log("✅ Schema test successful:");
    console.log(`   - Total events: ${testQuery.rows[0][0]}`);
    console.log(`   - Tenants: ${testQuery.rows[0][1]}`);
    console.log(`   - Latest event: ${testQuery.rows[0][2]}`);
    
    await client.end();
    
    console.log("🎉 Database schema fix complete!");
    console.log("");
    console.log("🚀 You can now restart the development server");
    
  } catch (error) {
    console.error("❌ Schema fix failed:", error);
    
    if (client) {
      try {
        await client.end();
      } catch {
        // Ignore cleanup errors
      }
    }
    
    Deno.exit(1);
  }
}

// Run the schema fix
if (import.meta.main) {
  await fixDatabaseSchema();
}
