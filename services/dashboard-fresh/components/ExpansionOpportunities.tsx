// Expansion Opportunities Component
// Identifies and displays revenue expansion opportunities for existing customers

import { useEffect, useRef } from "preact/hooks";
import * as d3 from "d3";

interface ExpansionOpportunity {
  customerId: string;
  customerName: string;
  currentPlan: string;
  currentMRR: number;
  recommendedPlan: string;
  potentialMRR: number;
  expansionValue: number;
  expansionType: 'upgrade' | 'add_on' | 'usage_increase' | 'seat_expansion';
  confidence: number;
  timeframe: string;
  triggers: string[];
  usageMetrics: {
    currentUsage: number;
    planLimit: number;
    utilizationRate: number;
    growthRate: number;
  };
  engagementScore: number;
  lastContact: string;
  priority: 'high' | 'medium' | 'low';
}

interface ExpansionData {
  totalOpportunities: number;
  totalPotentialRevenue: number;
  averageExpansionValue: number;
  conversionRate: number;
  opportunities: ExpansionOpportunity[];
  expansionTrends: Array<{
    month: string;
    opportunities: number;
    converted: number;
    revenue: number;
  }>;
  expansionByType: Record<string, {
    count: number;
    revenue: number;
    conversionRate: number;
  }>;
}

interface ExpansionOpportunitiesProps {
  expansionData: ExpansionData;
  isLoading: boolean;
  onContactCustomer?: (customerId: string, opportunityType: string) => void;
  onViewCustomerDetails?: (customerId: string) => void;
}

export function ExpansionOpportunities({ 
  expansionData, 
  isLoading, 
  onContactCustomer,
  onViewCustomerDetails 
}: ExpansionOpportunitiesProps) {
  const trendsChartRef = useRef<SVGSVGElement>(null);
  const typeDistributionRef = useRef<SVGSVGElement>(null);

  // Create expansion trends chart
  useEffect(() => {
    if (!trendsChartRef.current || isLoading || !expansionData.expansionTrends?.length) return;

    const svg = d3.select(trendsChartRef.current);
    svg.selectAll("*").remove();

    const width = 400;
    const height = 200;
    const margin = { top: 20, right: 30, bottom: 40, left: 50 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3.scaleBand()
      .domain(expansionData.expansionTrends.map(d => d.month))
      .range([0, chartWidth])
      .padding(0.1);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(expansionData.expansionTrends, d => d.opportunities) || 10])
      .range([chartHeight, 0]);

    // Bars for opportunities
    g.selectAll(".opportunity-bar")
      .data(expansionData.expansionTrends)
      .enter().append("rect")
      .attr("class", "opportunity-bar")
      .attr("x", d => xScale(d.month) || 0)
      .attr("y", d => yScale(d.opportunities))
      .attr("width", xScale.bandwidth() * 0.4)
      .attr("height", d => chartHeight - yScale(d.opportunities))
      .attr("fill", "#3b82f6");

    // Bars for converted
    g.selectAll(".converted-bar")
      .data(expansionData.expansionTrends)
      .enter().append("rect")
      .attr("class", "converted-bar")
      .attr("x", d => (xScale(d.month) || 0) + xScale.bandwidth() * 0.4)
      .attr("y", d => yScale(d.converted))
      .attr("width", xScale.bandwidth() * 0.4)
      .attr("height", d => chartHeight - yScale(d.converted))
      .attr("fill", "#10b981");

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale));

    g.append("g")
      .call(d3.axisLeft(yScale));

    // Add legend
    const legend = g.append("g")
      .attr("transform", `translate(${chartWidth - 120}, 10)`);

    legend.append("rect")
      .attr("x", 0)
      .attr("y", 0)
      .attr("width", 12)
      .attr("height", 12)
      .attr("fill", "#3b82f6");

    legend.append("text")
      .attr("x", 16)
      .attr("y", 10)
      .style("font-size", "12px")
      .text("Opportunities");

    legend.append("rect")
      .attr("x", 0)
      .attr("y", 20)
      .attr("width", 12)
      .attr("height", 12)
      .attr("fill", "#10b981");

    legend.append("text")
      .attr("x", 16)
      .attr("y", 30)
      .style("font-size", "12px")
      .text("Converted");

  }, [expansionData.expansionTrends, isLoading]);

  // Create expansion type distribution chart
  useEffect(() => {
    if (!typeDistributionRef.current || isLoading || !expansionData.expansionByType) return;

    const svg = d3.select(typeDistributionRef.current);
    svg.selectAll("*").remove();

    const width = 300;
    const height = 200;
    const margin = { top: 20, right: 20, bottom: 40, left: 80 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = Object.entries(expansionData.expansionByType).map(([type, data]) => ({
      type: type.replace(/_/g, ' ').toUpperCase(),
      count: data.count,
      revenue: data.revenue
    }));

    const xScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.count) || 10])
      .range([0, chartWidth]);

    const yScale = d3.scaleBand()
      .domain(data.map(d => d.type))
      .range([0, chartHeight])
      .padding(0.1);

    // Add bars
    g.selectAll(".type-bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "type-bar")
      .attr("x", 0)
      .attr("y", d => yScale(d.type) || 0)
      .attr("width", d => xScale(d.count))
      .attr("height", yScale.bandwidth())
      .attr("fill", "#8b5cf6");

    // Add value labels
    g.selectAll(".value-label")
      .data(data)
      .enter().append("text")
      .attr("class", "value-label")
      .attr("x", d => xScale(d.count) + 5)
      .attr("y", d => (yScale(d.type) || 0) + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .style("fill", "#374151")
      .text(d => d.count);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale));

    g.append("g")
      .call(d3.axisLeft(yScale));

  }, [expansionData.expansionByType, isLoading]);

  const getExpansionTypeIcon = (type: string): string => {
    switch (type) {
      case 'upgrade': return '⬆️';
      case 'add_on': return '➕';
      case 'usage_increase': return '📈';
      case 'seat_expansion': return '👥';
      default: return '💰';
    }
  };

  const getPriorityBadgeClass = (priority: string): string => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="animate-pulse">
          <div class="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div class="space-y-3">
            <div class="h-4 bg-gray-200 rounded"></div>
            <div class="h-4 bg-gray-200 rounded w-5/6"></div>
            <div class="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div class="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
          <h3 class="text-lg font-semibold text-gray-900">Expansion Opportunities</h3>
        </div>
        <div class="text-sm text-gray-500">
          {expansionData.totalOpportunities} opportunities identified
        </div>
      </div>

      {/* Summary Metrics */}
      <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{expansionData.totalOpportunities}</div>
          <div class="text-sm text-green-800">Total Opportunities</div>
        </div>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600">{formatCurrency(expansionData.totalPotentialRevenue)}</div>
          <div class="text-sm text-blue-800">Potential Revenue</div>
        </div>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-purple-600">{formatCurrency(expansionData.averageExpansionValue)}</div>
          <div class="text-sm text-purple-800">Avg Expansion Value</div>
        </div>
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-indigo-600">{formatPercentage(expansionData.conversionRate)}</div>
          <div class="text-sm text-indigo-800">Conversion Rate</div>
        </div>
      </div>

      {/* Charts */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Expansion Trends</h4>
          <svg ref={trendsChartRef} width="400" height="200" class="w-full"></svg>
        </div>
        <div>
          <h4 class="text-md font-medium text-gray-900 mb-3">Opportunities by Type</h4>
          <svg ref={typeDistributionRef} width="300" height="200" class="w-full"></svg>
        </div>
      </div>

      {/* Top Opportunities */}
      <div>
        <h4 class="text-md font-medium text-gray-900 mb-3">Top Expansion Opportunities</h4>
        <div class="space-y-3">
          {expansionData.opportunities
            .sort((a, b) => b.expansionValue - a.expansionValue)
            .slice(0, 5)
            .map((opportunity) => (
            <div key={opportunity.customerId} class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <span class="text-lg mr-2">{getExpansionTypeIcon(opportunity.expansionType)}</span>
                    <h5 class="font-medium text-gray-900 mr-3">{opportunity.customerName}</h5>
                    <span class={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityBadgeClass(opportunity.priority)}`}>
                      {opportunity.priority.toUpperCase()}
                    </span>
                    <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                      {formatPercentage(opportunity.confidence)} confidence
                    </span>
                  </div>
                  <div class="text-sm text-gray-600 mb-2">
                    {opportunity.currentPlan} → {opportunity.recommendedPlan} | 
                    Current: {formatCurrency(opportunity.currentMRR)}/mo | 
                    Potential: {formatCurrency(opportunity.potentialMRR)}/mo | 
                    Expansion: {formatCurrency(opportunity.expansionValue)}/mo
                  </div>
                  <div class="text-sm text-gray-600 mb-2">
                    Usage: {formatPercentage(opportunity.usageMetrics.utilizationRate)} of limit | 
                    Growth: {formatPercentage(opportunity.usageMetrics.growthRate)}/mo | 
                    Engagement: {opportunity.engagementScore}/100
                  </div>
                  <div class="flex flex-wrap gap-1">
                    {opportunity.triggers.slice(0, 3).map((trigger, index) => (
                      <span key={index} class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        {trigger}
                      </span>
                    ))}
                    {opportunity.triggers.length > 3 && (
                      <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        +{opportunity.triggers.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
                <div class="flex flex-col space-y-2 ml-4">
                  <button
                    onClick={() => onViewCustomerDetails?.(opportunity.customerId)}
                    class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                  >
                    View Details
                  </button>
                  <button
                    onClick={() => onContactCustomer?.(opportunity.customerId, opportunity.expansionType)}
                    class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                  >
                    Contact Customer
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Action Items */}
      {expansionData.totalOpportunities > 0 && (
        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 class="font-medium text-green-800 mb-2">💡 Recommended Actions</h4>
          <ul class="text-sm text-green-700 space-y-1">
            <li>• Focus on {expansionData.opportunities.filter(o => o.priority === 'high').length} high-priority opportunities first</li>
            <li>• Potential to increase revenue by {formatCurrency(expansionData.totalPotentialRevenue)} annually</li>
            <li>• Target customers with >80% usage utilization for upgrade conversations</li>
          </ul>
        </div>
      )}
    </div>
  );
}
