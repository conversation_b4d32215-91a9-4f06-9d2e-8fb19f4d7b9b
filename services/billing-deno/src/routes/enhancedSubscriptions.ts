import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { EnhancedSubscriptionService } from "../services/enhancedSubscriptionService.ts";
import { UnifiedRevenueOperationsService } from "../services/unifiedRevenueOperationsService.ts";
import { SubscriptionLifecycleService } from "../services/subscriptionLifecycleService.ts";
import { UsageBasedBillingService } from "../services/usageBasedBillingService.ts";
import { RevenueIntelligenceService } from "../services/revenueIntelligenceService.ts";
import { DemoEnvironmentService } from "../services/demoEnvironmentService.ts";
import { SalesMaterialsService } from "../services/salesMaterialsService.ts";
import { authMiddleware } from "../middleware/auth.ts";
import { ApiResponse } from "../utils/apiResponse.ts";

export const enhancedSubscriptionRoutes = new Router();

// Apply middleware
enhancedSubscriptionRoutes.use(authMiddleware);

// Initialize services
const enhancedSubscriptionService = new EnhancedSubscriptionService();
const unifiedRevenueService = new UnifiedRevenueOperationsService();
const subscriptionLifecycleService = new SubscriptionLifecycleService();
const usageBasedBillingService = new UsageBasedBillingService();
const revenueIntelligenceService = new RevenueIntelligenceService();
const demoEnvironmentService = new DemoEnvironmentService();
const salesMaterialsService = new SalesMaterialsService();

// Validation schemas
const DynamicPricingRequestSchema = z.object({
  customerId: z.string(),
  planId: z.string(),
  usageMetrics: z.object({
    apiCalls: z.number(),
    dataVolume: z.number(),
    teamSize: z.number(),
    featureUsage: z.record(z.number()),
  }),
  marketConditions: z.object({
    competitorPricing: z.number(),
    demandLevel: z.enum(['low', 'medium', 'high']),
    seasonality: z.number(),
  }).optional(),
  customerProfile: z.object({
    lifetimeValue: z.number(),
    churnRisk: z.number(),
    expansionProbability: z.number(),
    paymentReliability: z.number(),
  }).optional(),
});

const UsageBasedBillingRequestSchema = z.object({
  planId: z.string(),
  historicalUsage: z.array(z.object({
    period: z.string(),
    usage: z.number(),
    revenue: z.number(),
  })),
});

/**
 * Generate dynamic pricing recommendation
 * POST /api/enhanced-subscriptions/dynamic-pricing
 */
enhancedSubscriptionRoutes.post("/dynamic-pricing", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;
    const validatedData = DynamicPricingRequestSchema.parse(body);

    const startTime = performance.now();

    const recommendation = await enhancedSubscriptionService.generateDynamicPricing({
      tenantId,
      ...validatedData,
    });

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      recommendation,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Dynamic pricing recommendation generated", {
      tenantId,
      customerId: validatedData.customerId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      originalPrice: recommendation.originalPrice,
      recommendedPrice: recommendation.recommendedPrice,
      strategy: recommendation.pricingStrategy,
    });
  } catch (error) {
    logger.error("Failed to generate dynamic pricing recommendation", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Validation failed",
        "VALIDATION_ERROR",
        400,
        error.errors
      );
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Optimize usage-based billing configuration
 * POST /api/enhanced-subscriptions/optimize-usage-billing
 */
enhancedSubscriptionRoutes.post("/optimize-usage-billing", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;
    const validatedData = UsageBasedBillingRequestSchema.parse(body);

    const startTime = performance.now();

    const config = await enhancedSubscriptionService.optimizeUsageBasedBilling(
      tenantId,
      validatedData.planId,
      validatedData.historicalUsage
    );

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      config,
      summary: {
        basePrice: config.basePrice,
        tiersCount: config.usageTiers.length,
        billingCycle: config.billingCycle,
        aggregationMethod: config.aggregationMethod,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        periodsAnalyzed: validatedData.historicalUsage.length,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Usage-based billing configuration optimized", {
      tenantId,
      planId: validatedData.planId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      basePrice: config.basePrice,
      tiersCount: config.usageTiers.length,
    });
  } catch (error) {
    logger.error("Failed to optimize usage-based billing", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Validation failed",
        "VALIDATION_ERROR",
        400,
        error.errors
      );
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Generate tier recommendation for customer
 * GET /api/enhanced-subscriptions/tier-recommendation/:customerId
 */
enhancedSubscriptionRoutes.get("/tier-recommendation/:customerId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const customerId = ctx.params.customerId;

    if (!customerId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Customer ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();

    const recommendation = await enhancedSubscriptionService.generateTierRecommendation(
      tenantId,
      customerId
    );

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      recommendation,
      metadata: {
        tenantId,
        customerId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Tier recommendation generated", {
      tenantId,
      customerId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      currentTier: recommendation.currentTier,
      recommendedTier: recommendation.recommendedTier,
      reason: recommendation.reason,
    });
  } catch (error) {
    logger.error("Failed to generate tier recommendation", {
      tenantId: ctx.state.tenantId,
      customerId: ctx.params.customerId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get revenue intelligence insights for subscription
 * GET /api/enhanced-subscriptions/revenue-insights/:subscriptionId
 */
enhancedSubscriptionRoutes.get("/revenue-insights/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();

    const insights = await enhancedSubscriptionService.getRevenueIntelligenceInsights(
      tenantId,
      subscriptionId
    );

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      insights,
      summary: {
        healthScore: insights.subscriptionHealth.score,
        expansionOpportunities: insights.expansionOpportunities.length,
        retentionRisks: insights.retentionRisks.length,
        optimizationRecommendations: insights.optimizationRecommendations.length,
        totalPotentialRevenue: insights.expansionOpportunities.reduce((sum, o) => sum + o.potentialRevenue, 0),
      },
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Revenue intelligence insights generated", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      healthScore: insights.subscriptionHealth.score,
      expansionOpportunities: insights.expansionOpportunities.length,
      retentionRisks: insights.retentionRisks.length,
    });
  } catch (error) {
    logger.error("Failed to generate revenue intelligence insights", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get unified revenue operations dashboard
 * GET /api/enhanced-subscriptions/revenue-operations-dashboard
 */
enhancedSubscriptionRoutes.get("/revenue-operations-dashboard", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startTime = performance.now();

    const dashboard = await unifiedRevenueService.generateRevenueOperationsDashboard(tenantId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      dashboard,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Revenue operations dashboard generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      totalSubscriptions: dashboard.summary.totalSubscriptions,
      totalMRR: dashboard.summary.totalMRR,
      averageHealthScore: dashboard.summary.averageHealthScore,
    });
  } catch (error) {
    logger.error("Failed to generate revenue operations dashboard", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get unified subscription data with all revenue operations insights
 * GET /api/enhanced-subscriptions/unified-data/:subscriptionId
 */
enhancedSubscriptionRoutes.get("/unified-data/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();
    const unifiedData = await unifiedRevenueService.getUnifiedSubscriptionData(tenantId, subscriptionId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      unifiedData,
      summary: {
        healthScore: unifiedData.healthScore,
        riskLevel: unifiedData.riskLevel,
        expansionOpportunities: unifiedData.expansionOpportunities.length,
        churnProbability: unifiedData.churnPrediction.churnProbability,
        mrr: unifiedData.subscription.mrr,
      },
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Unified subscription data retrieved", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      healthScore: unifiedData.healthScore,
      riskLevel: unifiedData.riskLevel,
    });
  } catch (error) {
    logger.error("Failed to get unified subscription data", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Monitor subscription health and generate real-time alerts
 * GET /api/enhanced-subscriptions/health-monitoring
 */
enhancedSubscriptionRoutes.get("/health-monitoring", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startTime = performance.now();

    const alerts = await unifiedRevenueService.monitorSubscriptionHealth(tenantId);
    const queryTime = performance.now() - startTime;

    // Group alerts by severity
    const alertsSummary = {
      critical: alerts.filter(a => a.severity === 'critical').length,
      high: alerts.filter(a => a.severity === 'high').length,
      medium: alerts.filter(a => a.severity === 'medium').length,
      low: alerts.filter(a => a.severity === 'low').length,
      total: alerts.length,
    };

    ctx.response.body = ApiResponse.success({
      alerts,
      summary: alertsSummary,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Subscription health monitoring completed", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      totalAlerts: alerts.length,
      criticalAlerts: alertsSummary.critical,
    });
  } catch (error) {
    logger.error("Failed to monitor subscription health", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Optimize subscription pricing based on comprehensive data
 * POST /api/enhanced-subscriptions/optimize-pricing/:subscriptionId
 */
enhancedSubscriptionRoutes.post("/optimize-pricing/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();
    const optimization = await unifiedRevenueService.optimizeSubscriptionPricing(tenantId, subscriptionId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      optimization,
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Subscription pricing optimized", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      currentPrice: optimization.currentPricing.amount,
      optimizedPrice: optimization.optimizedPricing.recommendedPrice,
      expectedRevenueChange: optimization.expectedImpact.revenueChange,
    });
  } catch (error) {
    logger.error("Failed to optimize subscription pricing", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Process subscription event and trigger automated workflows
 * POST /api/enhanced-subscriptions/process-event/:subscriptionId
 */
enhancedSubscriptionRoutes.post("/process-event/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;
    const result = ctx.request.body({ type: "json" });
    const body = await result.value;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const eventSchema = z.object({
      eventType: z.string(),
      eventData: z.record(z.any()).optional().default({}),
    });

    const validatedData = eventSchema.parse(body);
    const startTime = performance.now();

    await unifiedRevenueService.processSubscriptionEvent(
      tenantId,
      subscriptionId,
      validatedData.eventType,
      validatedData.eventData
    );

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      message: "Subscription event processed successfully",
      eventType: validatedData.eventType,
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        processedAt: new Date().toISOString(),
      },
    });

    logger.info("Subscription event processed", {
      tenantId,
      subscriptionId,
      eventType: validatedData.eventType,
      queryTime: `${queryTime.toFixed(2)}ms`,
    });
  } catch (error) {
    logger.error("Failed to process subscription event", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get comprehensive usage analytics for subscription
 * GET /api/enhanced-subscriptions/usage-analytics/:subscriptionId
 */
enhancedSubscriptionRoutes.get("/usage-analytics/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;
    const startDate = ctx.request.url.searchParams.get("startDate");
    const endDate = ctx.request.url.searchParams.get("endDate");

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const period = {
      start: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: endDate ? new Date(endDate) : new Date(),
    };

    const startTime = performance.now();
    const analytics = await usageBasedBillingService.getUsageAnalytics(tenantId, subscriptionId, period);
    const tierRecommendation = await usageBasedBillingService.getTierRecommendation(tenantId, subscriptionId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      analytics,
      tierRecommendation,
      metadata: {
        tenantId,
        subscriptionId,
        period,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Usage analytics retrieved", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      metricsCount: analytics.metrics.length,
      recommendationsCount: analytics.recommendations.length,
    });
  } catch (error) {
    logger.error("Failed to get usage analytics", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get churn prediction with detailed analysis
 * GET /api/enhanced-subscriptions/churn-prediction/:subscriptionId
 */
enhancedSubscriptionRoutes.get("/churn-prediction/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();
    const churnPrediction = await subscriptionLifecycleService.predictChurnRisk(tenantId, subscriptionId);
    const renewalForecast = await subscriptionLifecycleService.generateRenewalForecast(tenantId, subscriptionId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      churnPrediction,
      renewalForecast,
      summary: {
        riskLevel: churnPrediction.riskLevel,
        churnProbability: churnPrediction.churnProbability,
        renewalProbability: renewalForecast.renewalProbability,
        expectedRevenue: renewalForecast.expectedRevenue,
        factorsCount: churnPrediction.factors.length,
        recommendationsCount: churnPrediction.recommendations.length,
      },
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Churn prediction retrieved", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      riskLevel: churnPrediction.riskLevel,
      churnProbability: churnPrediction.churnProbability,
    });
  } catch (error) {
    logger.error("Failed to get churn prediction", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get revenue intelligence insights
 * GET /api/enhanced-subscriptions/revenue-intelligence
 */
enhancedSubscriptionRoutes.get("/revenue-intelligence", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startTime = performance.now();

    const intelligence = await revenueIntelligenceService.generateRevenueIntelligence(tenantId);
    const scenarios = await revenueIntelligenceService.generateRevenueScenarios(tenantId, 12);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      intelligence,
      scenarios,
      summary: {
        opportunitiesCount: intelligence.opportunities.length,
        risksCount: intelligence.risks.length,
        insightsCount: intelligence.insights.length,
        recommendationsCount: intelligence.recommendations.length,
        nextMonthRevenue: intelligence.predictions.nextMonthRevenue.amount,
        confidence: intelligence.predictions.nextMonthRevenue.confidence,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Revenue intelligence retrieved", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      opportunitiesCount: intelligence.opportunities.length,
      risksCount: intelligence.risks.length,
      nextMonthRevenue: intelligence.predictions.nextMonthRevenue.amount,
    });
  } catch (error) {
    logger.error("Failed to get revenue intelligence", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * DEMO ENVIRONMENT ENDPOINTS
 * Specialized endpoints for sales demonstrations and customer trials
 */

/**
 * Generate comprehensive demo dashboard with performance metrics
 * GET /api/enhanced-subscriptions/demo/performance-dashboard
 */
enhancedSubscriptionRoutes.get("/demo/performance-dashboard", async (ctx) => {
  try {
    const scenarioId = ctx.request.url.searchParams.get("scenario") || "ecommerce_smb";
    const startTime = performance.now();

    // Use demo environment service to generate comprehensive demo data
    const demoData = await demoEnvironmentService.generateDemoDashboard(scenarioId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      demoData,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
        scenario: scenarioId,
      },
    });

    logger.info("Demo performance dashboard generated", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      scenario: scenarioId,
      eventIngestionRate: demoData.performanceMetrics.eventIngestionRate,
      queryResponseTime: demoData.performanceMetrics.queryResponseTime,
    });
  } catch (error) {
    logger.error("Failed to generate demo performance dashboard", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Generate real-time event stream simulation for demo
 * GET /api/enhanced-subscriptions/demo/event-stream
 */
enhancedSubscriptionRoutes.get("/demo/event-stream", (ctx) => {
  try {
    const startTime = performance.now();

    // Simulate real-time event processing
    const eventStream = [];
    const eventTypes = ['purchase', 'page_view', 'add_to_cart', 'checkout_start', 'user_signup'];
    const currentTime = Date.now();

    for (let i = 0; i < 100; i++) {
      eventStream.push({
        id: `event_${currentTime}_${i}`,
        type: eventTypes[Math.floor(Math.random() * eventTypes.length)],
        timestamp: new Date(currentTime - (i * 100)),
        processingTime: Math.random() * 2 + 1, // 1-3ms processing time
        userId: `user_${Math.floor(Math.random() * 10000)}`,
        revenue: eventTypes[Math.floor(Math.random() * eventTypes.length)] === 'purchase'
          ? Math.random() * 500 + 50 : null,
      });
    }

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      eventStream,
      streamMetrics: {
        eventsPerSecond: 24390,
        averageProcessingTime: 1.8,
        totalEventsProcessed: 2847392847,
        activeConnections: Math.floor(Math.random() * 500) + 1500,
      },
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Demo event stream generated", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      eventsCount: eventStream.length,
    });
  } catch (error) {
    logger.error("Failed to generate demo event stream", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Generate ROI calculation for demo customer
 * POST /api/enhanced-subscriptions/demo/roi-calculator
 */
enhancedSubscriptionRoutes.post("/demo/roi-calculator", async (ctx) => {
  try {
    const result = ctx.request.body({ type: "json" });
    const body = await result.value;
    const startTime = performance.now();

    // Use demo environment service for ROI calculation
    const roiData = await demoEnvironmentService.calculateCustomROI(body);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      ...roiData,
      metadata: {
        ...roiData.metadata,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Demo ROI calculation generated", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      monthlyEvents: roiData.customerProfile.monthlyEvents,
      roiPercentage: roiData.revenueImpact.summary.roiPercentage,
    });
  } catch (error) {
    logger.error("Failed to generate demo ROI calculation", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get available demo scenarios
 * GET /api/enhanced-subscriptions/demo/scenarios
 */
enhancedSubscriptionRoutes.get("/demo/scenarios", (ctx) => {
  try {
    const startTime = performance.now();

    const scenarios = demoEnvironmentService.getDemoScenarios();
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      scenarios,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Demo scenarios retrieved", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      scenariosCount: scenarios.length,
    });
  } catch (error) {
    logger.error("Failed to get demo scenarios", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get specific demo scenario details
 * GET /api/enhanced-subscriptions/demo/scenarios/:scenarioId
 */
enhancedSubscriptionRoutes.get("/demo/scenarios/:scenarioId", (ctx) => {
  try {
    const scenarioId = ctx.params.scenarioId;
    const startTime = performance.now();

    if (!scenarioId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Scenario ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const scenario = demoEnvironmentService.getDemoScenario(scenarioId);

    if (!scenario) {
      ctx.response.status = 404;
      ctx.response.body = ApiResponse.error(
        "Demo scenario not found",
        "SCENARIO_NOT_FOUND",
        404
      );
      return;
    }

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      scenario,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Demo scenario retrieved", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      scenarioId,
    });
  } catch (error) {
    logger.error("Failed to get demo scenario", {
      scenarioId: ctx.params.scenarioId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get industry-specific ROI templates
 * GET /api/enhanced-subscriptions/demo/industry-templates
 */
enhancedSubscriptionRoutes.get("/demo/industry-templates", (ctx) => {
  try {
    const startTime = performance.now();

    const templates = demoEnvironmentService.getIndustryTemplates();
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      templates,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Industry ROI templates retrieved", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      templatesCount: templates.length,
    });
  } catch (error) {
    logger.error("Failed to get industry ROI templates", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get comprehensive performance benchmarks
 * GET /api/enhanced-subscriptions/demo/performance-benchmarks
 */
enhancedSubscriptionRoutes.get("/demo/performance-benchmarks", (ctx) => {
  try {
    const startTime = performance.now();

    const benchmarks = demoEnvironmentService.getPerformanceBenchmarks();
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      benchmarks,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Performance benchmarks retrieved", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      benchmarksCount: benchmarks.length,
    });
  } catch (error) {
    logger.error("Failed to get performance benchmarks", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Generate comprehensive performance comparison report
 * POST /api/enhanced-subscriptions/demo/performance-comparison
 */
enhancedSubscriptionRoutes.post("/demo/performance-comparison", async (ctx) => {
  try {
    const result = ctx.request.body({ type: "json" });
    const body = await result.value;
    const startTime = performance.now();

    const includeCompetitors = body?.competitors || ["google_analytics", "mixpanel", "adobe_analytics"];
    const comparison = demoEnvironmentService.generatePerformanceComparison(includeCompetitors);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      ...comparison,
      metadata: {
        ...comparison.metadata,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Performance comparison generated", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      competitorsAnalyzed: includeCompetitors.length,
    });
  } catch (error) {
    logger.error("Failed to generate performance comparison", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * SALES MATERIALS ENDPOINTS
 * Generate comprehensive sales materials including reports, battle cards, and case studies
 */

/**
 * Generate ROI report for customer
 * POST /api/enhanced-subscriptions/demo/generate-roi-report
 */
enhancedSubscriptionRoutes.post("/demo/generate-roi-report", async (ctx) => {
  try {
    const result = ctx.request.body({ type: "json" });
    const body = await result.value;
    const startTime = performance.now();

    // Calculate ROI first
    const roiData = demoEnvironmentService.calculateCustomROI(body);

    // Generate report
    const report = salesMaterialsService.generateROIReport(body, roiData);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      report,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("ROI report generated", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      companyName: body.companyName,
      reportId: report.id,
    });
  } catch (error) {
    logger.error("Failed to generate ROI report", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get competitive battle cards
 * GET /api/enhanced-subscriptions/demo/battle-cards
 */
enhancedSubscriptionRoutes.get("/demo/battle-cards", (ctx) => {
  try {
    const startTime = performance.now();
    const competitor = ctx.request.url.searchParams.get("competitor");

    let battleCards;
    if (competitor) {
      const battleCard = salesMaterialsService.getBattleCard(competitor);
      battleCards = battleCard ? [battleCard] : [];
    } else {
      battleCards = salesMaterialsService.getAllBattleCards();
    }

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      battleCards,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Battle cards retrieved", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      competitor,
      battleCardsCount: battleCards.length,
    });
  } catch (error) {
    logger.error("Failed to get battle cards", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get case studies
 * GET /api/enhanced-subscriptions/demo/case-studies
 */
enhancedSubscriptionRoutes.get("/demo/case-studies", (ctx) => {
  try {
    const startTime = performance.now();
    const caseStudyId = ctx.request.url.searchParams.get("id");

    let caseStudies;
    if (caseStudyId) {
      const caseStudy = salesMaterialsService.getCaseStudy(caseStudyId);
      caseStudies = caseStudy ? [caseStudy] : [];
    } else {
      caseStudies = salesMaterialsService.getAllCaseStudies();
    }

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      caseStudies,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    });

    logger.info("Case studies retrieved", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      caseStudyId,
      caseStudiesCount: caseStudies.length,
    });
  } catch (error) {
    logger.error("Failed to get case studies", {
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

export { enhancedSubscriptionRoutes };
