import { Router } from "@oak/oak";
import { z } from "zod";
import { logger } from "../utils/logger.ts";
import { EnhancedSubscriptionService } from "../services/enhancedSubscriptionService.ts";
import { UnifiedRevenueOperationsService } from "../services/unifiedRevenueOperationsService.ts";
import { SubscriptionLifecycleService } from "../services/subscriptionLifecycleService.ts";
import { UsageBasedBillingService } from "../services/usageBasedBillingService.ts";
import { RevenueIntelligenceService } from "../services/revenueIntelligenceService.ts";
import { authMiddleware } from "../middleware/auth.ts";
import { ApiResponse } from "../utils/apiResponse.ts";

export const enhancedSubscriptionRoutes = new Router();

// Apply middleware
enhancedSubscriptionRoutes.use(authMiddleware);

// Initialize services
const enhancedSubscriptionService = new EnhancedSubscriptionService();
const unifiedRevenueService = new UnifiedRevenueOperationsService();
const subscriptionLifecycleService = new SubscriptionLifecycleService();
const usageBasedBillingService = new UsageBasedBillingService();
const revenueIntelligenceService = new RevenueIntelligenceService();

// Validation schemas
const DynamicPricingRequestSchema = z.object({
  customerId: z.string(),
  planId: z.string(),
  usageMetrics: z.object({
    apiCalls: z.number(),
    dataVolume: z.number(),
    teamSize: z.number(),
    featureUsage: z.record(z.number()),
  }),
  marketConditions: z.object({
    competitorPricing: z.number(),
    demandLevel: z.enum(['low', 'medium', 'high']),
    seasonality: z.number(),
  }).optional(),
  customerProfile: z.object({
    lifetimeValue: z.number(),
    churnRisk: z.number(),
    expansionProbability: z.number(),
    paymentReliability: z.number(),
  }).optional(),
});

const UsageBasedBillingRequestSchema = z.object({
  planId: z.string(),
  historicalUsage: z.array(z.object({
    period: z.string(),
    usage: z.number(),
    revenue: z.number(),
  })),
});

/**
 * Generate dynamic pricing recommendation
 * POST /api/enhanced-subscriptions/dynamic-pricing
 */
enhancedSubscriptionRoutes.post("/dynamic-pricing", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;
    const validatedData = DynamicPricingRequestSchema.parse(body);

    const startTime = performance.now();

    const recommendation = await enhancedSubscriptionService.generateDynamicPricing({
      tenantId,
      ...validatedData,
    });

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      recommendation,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Dynamic pricing recommendation generated", {
      tenantId,
      customerId: validatedData.customerId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      originalPrice: recommendation.originalPrice,
      recommendedPrice: recommendation.recommendedPrice,
      strategy: recommendation.pricingStrategy,
    });
  } catch (error) {
    logger.error("Failed to generate dynamic pricing recommendation", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Validation failed",
        "VALIDATION_ERROR",
        400,
        error.errors
      );
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Optimize usage-based billing configuration
 * POST /api/enhanced-subscriptions/optimize-usage-billing
 */
enhancedSubscriptionRoutes.post("/optimize-usage-billing", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const body = await ctx.request.body({ type: "json" }).value;
    const validatedData = UsageBasedBillingRequestSchema.parse(body);

    const startTime = performance.now();

    const config = await enhancedSubscriptionService.optimizeUsageBasedBilling(
      tenantId,
      validatedData.planId,
      validatedData.historicalUsage
    );

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      config,
      summary: {
        basePrice: config.basePrice,
        tiersCount: config.usageTiers.length,
        billingCycle: config.billingCycle,
        aggregationMethod: config.aggregationMethod,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        periodsAnalyzed: validatedData.historicalUsage.length,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Usage-based billing configuration optimized", {
      tenantId,
      planId: validatedData.planId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      basePrice: config.basePrice,
      tiersCount: config.usageTiers.length,
    });
  } catch (error) {
    logger.error("Failed to optimize usage-based billing", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    if (error instanceof z.ZodError) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Validation failed",
        "VALIDATION_ERROR",
        400,
        error.errors
      );
      return;
    }

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Generate tier recommendation for customer
 * GET /api/enhanced-subscriptions/tier-recommendation/:customerId
 */
enhancedSubscriptionRoutes.get("/tier-recommendation/:customerId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const customerId = ctx.params.customerId;

    if (!customerId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Customer ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();

    const recommendation = await enhancedSubscriptionService.generateTierRecommendation(
      tenantId,
      customerId
    );

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      recommendation,
      metadata: {
        tenantId,
        customerId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Tier recommendation generated", {
      tenantId,
      customerId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      currentTier: recommendation.currentTier,
      recommendedTier: recommendation.recommendedTier,
      reason: recommendation.reason,
    });
  } catch (error) {
    logger.error("Failed to generate tier recommendation", {
      tenantId: ctx.state.tenantId,
      customerId: ctx.params.customerId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get revenue intelligence insights for subscription
 * GET /api/enhanced-subscriptions/revenue-insights/:subscriptionId
 */
enhancedSubscriptionRoutes.get("/revenue-insights/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();

    const insights = await enhancedSubscriptionService.getRevenueIntelligenceInsights(
      tenantId,
      subscriptionId
    );

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      insights,
      summary: {
        healthScore: insights.subscriptionHealth.score,
        expansionOpportunities: insights.expansionOpportunities.length,
        retentionRisks: insights.retentionRisks.length,
        optimizationRecommendations: insights.optimizationRecommendations.length,
        totalPotentialRevenue: insights.expansionOpportunities.reduce((sum, o) => sum + o.potentialRevenue, 0),
      },
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Revenue intelligence insights generated", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      healthScore: insights.subscriptionHealth.score,
      expansionOpportunities: insights.expansionOpportunities.length,
      retentionRisks: insights.retentionRisks.length,
    });
  } catch (error) {
    logger.error("Failed to generate revenue intelligence insights", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get unified revenue operations dashboard
 * GET /api/enhanced-subscriptions/revenue-operations-dashboard
 */
enhancedSubscriptionRoutes.get("/revenue-operations-dashboard", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startTime = performance.now();

    const dashboard = await unifiedRevenueService.generateRevenueOperationsDashboard(tenantId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      dashboard,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Revenue operations dashboard generated", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      totalSubscriptions: dashboard.summary.totalSubscriptions,
      totalMRR: dashboard.summary.totalMRR,
      averageHealthScore: dashboard.summary.averageHealthScore,
    });
  } catch (error) {
    logger.error("Failed to generate revenue operations dashboard", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get unified subscription data with all revenue operations insights
 * GET /api/enhanced-subscriptions/unified-data/:subscriptionId
 */
enhancedSubscriptionRoutes.get("/unified-data/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();
    const unifiedData = await unifiedRevenueService.getUnifiedSubscriptionData(tenantId, subscriptionId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      unifiedData,
      summary: {
        healthScore: unifiedData.healthScore,
        riskLevel: unifiedData.riskLevel,
        expansionOpportunities: unifiedData.expansionOpportunities.length,
        churnProbability: unifiedData.churnPrediction.churnProbability,
        mrr: unifiedData.subscription.mrr,
      },
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Unified subscription data retrieved", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      healthScore: unifiedData.healthScore,
      riskLevel: unifiedData.riskLevel,
    });
  } catch (error) {
    logger.error("Failed to get unified subscription data", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Monitor subscription health and generate real-time alerts
 * GET /api/enhanced-subscriptions/health-monitoring
 */
enhancedSubscriptionRoutes.get("/health-monitoring", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startTime = performance.now();

    const alerts = await unifiedRevenueService.monitorSubscriptionHealth(tenantId);
    const queryTime = performance.now() - startTime;

    // Group alerts by severity
    const alertsSummary = {
      critical: alerts.filter(a => a.severity === 'critical').length,
      high: alerts.filter(a => a.severity === 'high').length,
      medium: alerts.filter(a => a.severity === 'medium').length,
      low: alerts.filter(a => a.severity === 'low').length,
      total: alerts.length,
    };

    ctx.response.body = ApiResponse.success({
      alerts,
      summary: alertsSummary,
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Subscription health monitoring completed", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      totalAlerts: alerts.length,
      criticalAlerts: alertsSummary.critical,
    });
  } catch (error) {
    logger.error("Failed to monitor subscription health", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Optimize subscription pricing based on comprehensive data
 * POST /api/enhanced-subscriptions/optimize-pricing/:subscriptionId
 */
enhancedSubscriptionRoutes.post("/optimize-pricing/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();
    const optimization = await unifiedRevenueService.optimizeSubscriptionPricing(tenantId, subscriptionId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      optimization,
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Subscription pricing optimized", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      currentPrice: optimization.currentPricing.amount,
      optimizedPrice: optimization.optimizedPricing.recommendedPrice,
      expectedRevenueChange: optimization.expectedImpact.revenueChange,
    });
  } catch (error) {
    logger.error("Failed to optimize subscription pricing", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Process subscription event and trigger automated workflows
 * POST /api/enhanced-subscriptions/process-event/:subscriptionId
 */
enhancedSubscriptionRoutes.post("/process-event/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;
    const result = ctx.request.body({ type: "json" });
    const body = await result.value;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const eventSchema = z.object({
      eventType: z.string(),
      eventData: z.record(z.any()).optional().default({}),
    });

    const validatedData = eventSchema.parse(body);
    const startTime = performance.now();

    await unifiedRevenueService.processSubscriptionEvent(
      tenantId,
      subscriptionId,
      validatedData.eventType,
      validatedData.eventData
    );

    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      message: "Subscription event processed successfully",
      eventType: validatedData.eventType,
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        processedAt: new Date().toISOString(),
      },
    });

    logger.info("Subscription event processed", {
      tenantId,
      subscriptionId,
      eventType: validatedData.eventType,
      queryTime: `${queryTime.toFixed(2)}ms`,
    });
  } catch (error) {
    logger.error("Failed to process subscription event", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get comprehensive usage analytics for subscription
 * GET /api/enhanced-subscriptions/usage-analytics/:subscriptionId
 */
enhancedSubscriptionRoutes.get("/usage-analytics/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;
    const startDate = ctx.request.url.searchParams.get("startDate");
    const endDate = ctx.request.url.searchParams.get("endDate");

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const period = {
      start: startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: endDate ? new Date(endDate) : new Date(),
    };

    const startTime = performance.now();
    const analytics = await usageBasedBillingService.getUsageAnalytics(tenantId, subscriptionId, period);
    const tierRecommendation = await usageBasedBillingService.getTierRecommendation(tenantId, subscriptionId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      analytics,
      tierRecommendation,
      metadata: {
        tenantId,
        subscriptionId,
        period,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Usage analytics retrieved", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      metricsCount: analytics.metrics.length,
      recommendationsCount: analytics.recommendations.length,
    });
  } catch (error) {
    logger.error("Failed to get usage analytics", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get churn prediction with detailed analysis
 * GET /api/enhanced-subscriptions/churn-prediction/:subscriptionId
 */
enhancedSubscriptionRoutes.get("/churn-prediction/:subscriptionId", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const subscriptionId = ctx.params.subscriptionId;

    if (!subscriptionId) {
      ctx.response.status = 400;
      ctx.response.body = ApiResponse.error(
        "Subscription ID is required",
        "MISSING_PARAMETER",
        400
      );
      return;
    }

    const startTime = performance.now();
    const churnPrediction = await subscriptionLifecycleService.predictChurnRisk(tenantId, subscriptionId);
    const renewalForecast = await subscriptionLifecycleService.generateRenewalForecast(tenantId, subscriptionId);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      churnPrediction,
      renewalForecast,
      summary: {
        riskLevel: churnPrediction.riskLevel,
        churnProbability: churnPrediction.churnProbability,
        renewalProbability: renewalForecast.renewalProbability,
        expectedRevenue: renewalForecast.expectedRevenue,
        factorsCount: churnPrediction.factors.length,
        recommendationsCount: churnPrediction.recommendations.length,
      },
      metadata: {
        tenantId,
        subscriptionId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Churn prediction retrieved", {
      tenantId,
      subscriptionId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      riskLevel: churnPrediction.riskLevel,
      churnProbability: churnPrediction.churnProbability,
    });
  } catch (error) {
    logger.error("Failed to get churn prediction", {
      tenantId: ctx.state.tenantId,
      subscriptionId: ctx.params.subscriptionId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});

/**
 * Get revenue intelligence insights
 * GET /api/enhanced-subscriptions/revenue-intelligence
 */
enhancedSubscriptionRoutes.get("/revenue-intelligence", async (ctx) => {
  try {
    const tenantId = ctx.state.tenantId;
    const startTime = performance.now();

    const intelligence = await revenueIntelligenceService.generateRevenueIntelligence(tenantId);
    const scenarios = await revenueIntelligenceService.generateRevenueScenarios(tenantId, 12);
    const queryTime = performance.now() - startTime;

    ctx.response.body = ApiResponse.success({
      intelligence,
      scenarios,
      summary: {
        opportunitiesCount: intelligence.opportunities.length,
        risksCount: intelligence.risks.length,
        insightsCount: intelligence.insights.length,
        recommendationsCount: intelligence.recommendations.length,
        nextMonthRevenue: intelligence.predictions.nextMonthRevenue.amount,
        confidence: intelligence.predictions.nextMonthRevenue.confidence,
      },
      metadata: {
        tenantId,
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
      },
    });

    logger.info("Revenue intelligence retrieved", {
      tenantId,
      queryTime: `${queryTime.toFixed(2)}ms`,
      opportunitiesCount: intelligence.opportunities.length,
      risksCount: intelligence.risks.length,
      nextMonthRevenue: intelligence.predictions.nextMonthRevenue.amount,
    });
  } catch (error) {
    logger.error("Failed to get revenue intelligence", {
      tenantId: ctx.state.tenantId,
      error: (error as Error).message,
    });

    ctx.response.status = 500;
    ctx.response.body = ApiResponse.error(
      "Internal server error",
      "INTERNAL_ERROR",
      500
    );
  }
});
