// Demo Environment Service
// Specialized service for generating sales demonstration data and customer trial environments

import { logger } from "../utils/logger.ts";

export interface DemoCustomerProfile {
  companyName: string;
  industry: string;
  monthlyEvents: number;
  currentAnalyticsCost: number;
  teamSize: number;
  currentQueryTime: number;
  painPoints: string[];
  goals: string[];
}

export interface PerformanceComparison {
  competitor: string;
  queryResponseTime: number;
  eventProcessingDelay: number;
  realTimeCapability: boolean;
  dataAccuracy: number;
  costPerEvent: number;
  setupTime: number; // hours
}

export interface DemoScenario {
  id: string;
  name: string;
  description: string;
  customerProfile: DemoCustomerProfile;
  performanceMetrics: any;
  roiProjection: any;
  competitiveAdvantages: string[];
}

export class DemoEnvironmentService {
  private demoScenarios: Map<string, DemoScenario> = new Map();
  private performanceBaselines: Map<string, PerformanceComparison> = new Map();

  constructor() {
    this.initializeDemoData();
  }

  /**
   * Initialize demo scenarios and competitive baselines
   */
  private initializeDemoData(): void {
    // Competitive performance baselines
    this.performanceBaselines.set("google_analytics", {
      competitor: "Google Analytics",
      queryResponseTime: 2500,
      eventProcessingDelay: 24 * 60 * 60 * 1000, // 24 hours
      realTimeCapability: false,
      dataAccuracy: 85.2,
      costPerEvent: 0.0005,
      setupTime: 48,
    });

    this.performanceBaselines.set("mixpanel", {
      competitor: "Mixpanel",
      queryResponseTime: 1200,
      eventProcessingDelay: 2 * 60 * 60 * 1000, // 2 hours
      realTimeCapability: true,
      dataAccuracy: 92.1,
      costPerEvent: 0.0008,
      setupTime: 24,
    });

    this.performanceBaselines.set("adobe_analytics", {
      competitor: "Adobe Analytics",
      queryResponseTime: 3200,
      eventProcessingDelay: 4 * 60 * 60 * 1000, // 4 hours
      realTimeCapability: false,
      dataAccuracy: 88.7,
      costPerEvent: 0.0012,
      setupTime: 72,
    });

    this.performanceBaselines.set("our_platform", {
      competitor: "Our Platform",
      queryResponseTime: 8, // 6-11ms average
      eventProcessingDelay: 50, // 50ms
      realTimeCapability: true,
      dataAccuracy: 99.95,
      costPerEvent: 0.0002,
      setupTime: 0.25, // 15 minutes
    });

    // Demo scenarios
    this.createDemoScenarios();
  }

  /**
   * Create predefined demo scenarios for different customer types
   */
  private createDemoScenarios(): void {
    // E-commerce SMB scenario
    this.demoScenarios.set("ecommerce_smb", {
      id: "ecommerce_smb",
      name: "E-commerce SMB Growth",
      description: "Small to medium e-commerce business looking to optimize conversion rates",
      customerProfile: {
        companyName: "TechGear Plus",
        industry: "E-commerce Electronics",
        monthlyEvents: 500000,
        currentAnalyticsCost: 2500,
        teamSize: 5,
        currentQueryTime: 2500,
        painPoints: [
          "Delayed insights affecting marketing decisions",
          "High analytics costs eating into margins",
          "Complex setup requiring technical expertise",
          "Limited real-time capabilities"
        ],
        goals: [
          "Reduce customer acquisition cost by 25%",
          "Improve conversion rates by 15%",
          "Get real-time insights for marketing optimization",
          "Reduce analytics costs while improving capabilities"
        ]
      },
      performanceMetrics: {},
      roiProjection: {},
      competitiveAdvantages: [
        "97% faster query response times",
        "Real-time event processing vs 24-hour delays",
        "60% cost reduction with better features",
        "15-minute setup vs 48-hour implementation"
      ]
    });

    // Enterprise scenario
    this.demoScenarios.set("enterprise_retail", {
      id: "enterprise_retail",
      name: "Enterprise Retail Chain",
      description: "Large retail chain with multiple channels and complex analytics needs",
      customerProfile: {
        companyName: "MegaRetail Corp",
        industry: "Multi-channel Retail",
        monthlyEvents: 10000000,
        currentAnalyticsCost: 25000,
        teamSize: 25,
        currentQueryTime: 3200,
        painPoints: [
          "Siloed analytics across channels",
          "Expensive enterprise analytics licenses",
          "Slow query performance affecting operations",
          "Limited predictive capabilities"
        ],
        goals: [
          "Unify analytics across all channels",
          "Implement predictive analytics for inventory",
          "Reduce analytics infrastructure costs",
          "Enable real-time personalization"
        ]
      },
      performanceMetrics: {},
      roiProjection: {},
      competitiveAdvantages: [
        "Unified multi-tenant architecture",
        "Advanced ML-powered predictions",
        "Enterprise-grade security and compliance",
        "Massive scale with consistent performance"
      ]
    });
  }

  /**
   * Generate comprehensive demo dashboard data
   */
  async generateDemoDashboard(scenarioId?: string): Promise<any> {
    const startTime = performance.now();
    
    const scenario = scenarioId ? this.demoScenarios.get(scenarioId) : 
      this.demoScenarios.get("ecommerce_smb");
    
    if (!scenario) {
      throw new Error(`Demo scenario not found: ${scenarioId}`);
    }

    // Generate real-time performance metrics
    const performanceMetrics = {
      eventIngestionRate: 24390 + Math.floor(Math.random() * 1000), // 24,390+ events/sec
      queryResponseTime: Math.random() * 5 + 6, // 6-11ms range
      systemUptime: 99.97 + Math.random() * 0.02,
      dataAccuracy: 99.95 + Math.random() * 0.04,
      compressionRatio: 70 + Math.random() * 5, // 70-75%
      concurrentUsers: Math.floor(Math.random() * 500) + 1500,
      activeConnections: Math.floor(Math.random() * 200) + 800,
      memoryUsage: Math.random() * 20 + 45, // 45-65%
      cpuUsage: Math.random() * 15 + 25, // 25-40%
    };

    // Generate competitive comparison
    const competitiveComparison = Array.from(this.performanceBaselines.values()).map(baseline => ({
      ...baseline,
      performanceAdvantage: baseline.competitor === "Our Platform" ? 0 : 
        ((baseline.queryResponseTime - 8) / baseline.queryResponseTime * 100),
      costAdvantage: baseline.competitor === "Our Platform" ? 0 :
        ((baseline.costPerEvent - 0.0002) / baseline.costPerEvent * 100),
    }));

    // Generate revenue impact projections
    const revenueImpact = this.calculateRevenueImpact(scenario.customerProfile);

    const queryTime = performance.now() - startTime;

    const demoData = {
      scenario,
      performanceMetrics,
      competitiveComparison,
      revenueImpact,
      realTimeMetrics: this.generateRealTimeMetrics(),
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    };

    logger.info("Demo dashboard generated", {
      scenarioId: scenario.id,
      queryTime: `${queryTime.toFixed(2)}ms`,
      eventIngestionRate: performanceMetrics.eventIngestionRate,
    });

    return demoData;
  }

  /**
   * Calculate revenue impact for customer profile
   */
  private calculateRevenueImpact(profile: DemoCustomerProfile): any {
    const currentCosts = {
      analyticsTools: profile.currentAnalyticsCost,
      developerTime: profile.teamSize * 2000, // $2000/month per developer
      infrastructureCosts: profile.monthlyEvents * 0.0005,
      opportunityCost: profile.currentAnalyticsCost * 0.3, // 30% opportunity cost
    };

    const projectedSavings = {
      analyticsToolReplacement: currentCosts.analyticsTools * 0.6, // 60% cost reduction
      developerTimeValue: currentCosts.developerTime * 0.4, // 40% time savings
      infrastructureSavings: profile.monthlyEvents * 0.0003, // $0.0003 per event saved
      fasterDecisionMaking: currentCosts.opportunityCost * 0.8, // 80% of opportunity cost recovered
    };

    const revenueGains = {
      conversionImprovement: profile.currentAnalyticsCost * 0.25, // 25% revenue increase
      churnReduction: profile.currentAnalyticsCost * 0.15, // 15% churn reduction value
      newInsightsValue: profile.teamSize * 1500, // $1500/month per team member
      realTimeOptimization: profile.monthlyEvents * 0.0001, // $0.0001 per event optimized
    };

    const totalMonthlySavings = Object.values(projectedSavings).reduce((a, b) => a + b, 0);
    const totalMonthlyRevenue = Object.values(revenueGains).reduce((a, b) => a + b, 0);
    const totalMonthlyValue = totalMonthlySavings + totalMonthlyRevenue;
    
    return {
      currentCosts,
      projectedSavings,
      revenueGains,
      summary: {
        totalMonthlySavings: Math.round(totalMonthlySavings),
        totalMonthlyRevenue: Math.round(totalMonthlyRevenue),
        totalMonthlyValue: Math.round(totalMonthlyValue),
        annualValue: Math.round(totalMonthlyValue * 12),
        roiPercentage: Math.round((totalMonthlyValue / profile.currentAnalyticsCost) * 100),
        paybackPeriod: Math.ceil(profile.currentAnalyticsCost / totalMonthlyValue), // months
      },
    };
  }

  /**
   * Generate real-time metrics for live demo
   */
  private generateRealTimeMetrics(): any {
    const now = Date.now();
    const metrics = [];
    
    for (let i = 0; i < 60; i++) {
      metrics.push({
        timestamp: new Date(now - (i * 1000)),
        eventsPerSecond: 24390 + Math.floor(Math.random() * 1000),
        queryResponseTime: Math.random() * 5 + 6,
        activeUsers: Math.floor(Math.random() * 100) + 1500,
        revenue: Math.random() * 1000 + 500,
      });
    }
    
    return metrics.reverse(); // Most recent first
  }

  /**
   * Get available demo scenarios
   */
  getDemoScenarios(): DemoScenario[] {
    return Array.from(this.demoScenarios.values());
  }

  /**
   * Get specific demo scenario
   */
  getDemoScenario(scenarioId: string): DemoScenario | undefined {
    return this.demoScenarios.get(scenarioId);
  }

  /**
   * Generate custom ROI calculation
   */
  async calculateCustomROI(customerData: any): Promise<any> {
    const startTime = performance.now();
    
    const profile: DemoCustomerProfile = {
      companyName: customerData.companyName || "Demo Company",
      industry: customerData.industry || "E-commerce",
      monthlyEvents: customerData.monthlyEvents || 1000000,
      currentAnalyticsCost: customerData.currentAnalyticsCost || 5000,
      teamSize: customerData.teamSize || 10,
      currentQueryTime: customerData.currentQueryTime || 2500,
      painPoints: customerData.painPoints || [],
      goals: customerData.goals || [],
    };

    const revenueImpact = this.calculateRevenueImpact(profile);
    const competitiveComparison = Array.from(this.performanceBaselines.values());
    
    const queryTime = performance.now() - startTime;

    logger.info("Custom ROI calculation generated", {
      queryTime: `${queryTime.toFixed(2)}ms`,
      monthlyEvents: profile.monthlyEvents,
      roiPercentage: revenueImpact.summary.roiPercentage,
    });

    return {
      customerProfile: profile,
      revenueImpact,
      competitiveComparison,
      metadata: {
        queryTime: `${queryTime.toFixed(2)}ms`,
        generatedAt: new Date().toISOString(),
        demoMode: true,
      },
    };
  }
}
